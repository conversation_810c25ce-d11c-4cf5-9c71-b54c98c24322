import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { BREAKPOINTS, CONTAINER_WIDTHS, SPACING } from '../constants/breakpoints';

// Styled components for responsive layout
const LayoutContainer = styled.div`
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  width: 100%;
  background-color: ${props => props.bgColor || '#ffffff'};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: ${props => props.sticky ? 'sticky' : 'static'};
  top: 0;
  z-index: 1000;
`;

const HeaderContent = styled.div`
  max-width: ${CONTAINER_WIDTHS.DESKTOP};
  margin: 0 auto;
  padding: ${SPACING.MD} ${SPACING.LG};

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: ${SPACING.MD};
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding: ${SPACING.SM};
  }
`;

const Main = styled.main`
  flex: 1;
  width: 100%;
  padding-top: 80px; /* Height of fixed navigation */

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding-top: 70px;
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding-top: 60px;
  }
`;

const MainContent = styled.div`
  max-width: ${CONTAINER_WIDTHS.DESKTOP};
  margin: 0 auto;
  padding: ${props => props.noPadding ? '0' : `${SPACING.XL} ${SPACING.LG}`};

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: ${props => props.noPadding ? '0' : `${SPACING.LG} ${SPACING.MD}`};
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding: ${props => props.noPadding ? '0' : `${SPACING.MD} ${SPACING.SM}`};
  }
`;

const Footer = styled.footer`
  width: 100%;
  background-color: ${props => props.bgColor || '#f8f9fa'};
  border-top: 1px solid #e9ecef;
  margin-top: auto;
`;

const FooterContent = styled.div`
  max-width: ${CONTAINER_WIDTHS.DESKTOP};
  margin: 0 auto;
  padding: ${SPACING.LG};

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: ${SPACING.MD};
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding: ${SPACING.SM};
  }
`;

const Sidebar = styled.aside`
  width: ${props => props.width || '250px'};
  background-color: ${props => props.bgColor || '#ffffff'};
  border-right: 1px solid #e9ecef;
  position: ${props => props.fixed ? 'fixed' : 'static'};
  top: ${props => props.headerHeight || '0'};
  left: 0;
  height: ${props => props.fixed ? `calc(100vh - ${props.headerHeight || '0px'})` : 'auto'};
  overflow-y: auto;
  z-index: 999;

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    width: ${props => props.tabletWidth || '200px'};
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: ${props => props.isOpen ? '0' : '-100%'};
    transition: left 0.3s ease-in-out;
    z-index: 1001;
  }
`;

const ContentWithSidebar = styled.div`
  display: flex;
  min-height: calc(100vh - ${props => props.headerHeight || '0px'} - ${props => props.footerHeight || '0px'});

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    flex-direction: column;
  }
`;

const MainWithSidebar = styled.main`
  flex: 1;
  margin-left: ${props => props.sidebarWidth || '250px'};

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    margin-left: ${props => props.tabletSidebarWidth || '200px'};
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    margin-left: 0;
  }
`;

/**
 * Responsive Layout Component
 * Provides a flexible layout structure that adapts to different screen sizes
 */
const ResponsiveLayout = ({
  children,
  header,
  footer,
  sidebar,
  stickyHeader = false,
  headerBgColor,
  footerBgColor,
  sidebarBgColor,
  sidebarWidth,
  sidebarTabletWidth,
  sidebarFixed = false,
  sidebarOpen = false,
  onSidebarToggle,
  noPadding = false,
  className
}) => {
  if (sidebar) {
    return (
      <LayoutContainer className={className}>
        {header && (
          <Header sticky={stickyHeader} bgColor={headerBgColor}>
            <HeaderContent>{header}</HeaderContent>
          </Header>
        )}

        <ContentWithSidebar>
          <Sidebar
            width={sidebarWidth}
            tabletWidth={sidebarTabletWidth}
            bgColor={sidebarBgColor}
            fixed={sidebarFixed}
            isOpen={sidebarOpen}
            headerHeight={header ? '70px' : '0px'}
          >
            {sidebar}
          </Sidebar>

          <MainWithSidebar
            sidebarWidth={sidebarWidth}
            tabletSidebarWidth={sidebarTabletWidth}
          >
            <MainContent noPadding={noPadding}>
              {children}
            </MainContent>
          </MainWithSidebar>
        </ContentWithSidebar>

        {footer && (
          <Footer bgColor={footerBgColor}>
            <FooterContent>{footer}</FooterContent>
          </Footer>
        )}
      </LayoutContainer>
    );
  }

  return (
    <LayoutContainer className={className}>
      {header && (
        <Header sticky={stickyHeader} bgColor={headerBgColor}>
          <HeaderContent>{header}</HeaderContent>
        </Header>
      )}

      <Main>
        <MainContent noPadding={noPadding}>
          {children}
        </MainContent>
      </Main>

      {footer && (
        <Footer bgColor={footerBgColor}>
          <FooterContent>{footer}</FooterContent>
        </Footer>
      )}
    </LayoutContainer>
  );
};

ResponsiveLayout.propTypes = {
  children: PropTypes.node.isRequired,
  header: PropTypes.node,
  footer: PropTypes.node,
  sidebar: PropTypes.node,
  stickyHeader: PropTypes.bool,
  headerBgColor: PropTypes.string,
  footerBgColor: PropTypes.string,
  sidebarBgColor: PropTypes.string,
  sidebarWidth: PropTypes.string,
  sidebarTabletWidth: PropTypes.string,
  sidebarFixed: PropTypes.bool,
  sidebarOpen: PropTypes.bool,
  onSidebarToggle: PropTypes.func,
  noPadding: PropTypes.bool,
  className: PropTypes.string
};

export default ResponsiveLayout;
