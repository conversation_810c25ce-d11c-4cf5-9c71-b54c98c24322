// Responsive breakpoints configuration
export const BREAKPOINTS = {
  PHONE: {
    MAX: 809,
    MEDIA_QUERY: '(max-width: 809px)'
  },
  TABLET: {
    MIN: 810,
    MAX: 1199,
    MEDIA_QUERY: '(min-width: 810px) and (max-width: 1199px)'
  },
  DESKTOP: {
    MIN: 1200,
    MEDIA_QUERY: '(min-width: 1200px)'
  }
};

// Container max widths for each breakpoint
export const CONTAINER_WIDTHS = {
  PHONE: '100%',
  TABLET: '100%',
  DESKTOP: '1200px'
};

// Common spacing values
export const SPACING = {
  XS: '0.25rem',
  SM: '0.5rem',
  MD: '1rem',
  LG: '1.5rem',
  XL: '2rem',
  XXL: '3rem',
  XXXL: '4rem'
};

// Typography scale
export const FONT_SIZES = {
  XS: '0.75rem',
  SM: '0.875rem',
  BASE: '1rem',
  LG: '1.125rem',
  XL: '1.25rem',
  XXL: '1.5rem',
  XXXL: '1.875rem',
  XXXXL: '2.25rem'
};

// Color palette
export const COLORS = {
  PRIMARY: '#007bff',
  SECONDARY: '#6c757d',
  SUCCESS: '#28a745',
  DANGER: '#dc3545',
  WARNING: '#ffc107',
  INFO: '#17a2b8',
  LIGHT: '#f8f9fa',
  DARK: '#343a40',
  WHITE: '#ffffff',
  BLACK: '#000000'
};

// Z-index scale
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070
};

// Animation durations
export const TRANSITIONS = {
  FAST: '0.15s',
  NORMAL: '0.3s',
  SLOW: '0.5s'
};

// Shadow levels
export const SHADOWS = {
  SM: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  MD: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  LG: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  XL: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
};

// Border radius values
export const BORDER_RADIUS = {
  SM: '0.25rem',
  MD: '0.375rem',
  LG: '0.5rem',
  XL: '0.75rem',
  XXL: '1rem',
  FULL: '9999px'
};
