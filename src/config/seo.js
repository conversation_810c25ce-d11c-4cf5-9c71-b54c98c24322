// SEO Configuration
export const SEO_CONFIG = {
  // Default meta tags
  defaultTitle: 'Langit Biru',
  titleTemplate: '%s | Langit Biru',
  defaultDescription: 'Professional React Application with Responsive Design. Built with modern tools and best practices for optimal user experience across all devices.',

  // Site info
  siteUrl: import.meta.env.VITE_BASE_URL || 'https://langit-biru.com',
  siteName: 'Langit Biru',
  author: 'Langit Biru Team',

  // Social media
  social: {
    twitter: '@langitbiru',
    facebook: 'langitbiru',
    instagram: 'langitbiru'
  },

  // Default Open Graph image
  defaultImage: '/og-image.jpg',

  // Theme
  themeColor: '#007bff',

  // Language
  language: 'id-ID',
  locale: 'id_ID',

  // Keywords
  defaultKeywords: [
    'react',
    'responsive design',
    'web development',
    'vite',
    'professional',
    'modern web app',
    'javascript',
    'frontend',
    'ui/ux'
  ]
};

// Page-specific SEO data
export const PAGE_SEO = {
  home: {
    title: 'Home',
    description: 'Langit Biru - Professional React application with responsive design. Experience modern web development with optimal performance across all devices.',
    keywords: ['home', 'landing page', 'react app', 'responsive', 'modern design']
  },

  about: {
    title: 'About',
    description: 'Learn about Langit Biru project - a showcase of professional React development, responsive design patterns, and modern web technologies.',
    keywords: ['about', 'project info', 'react features', 'development', 'technology stack']
  }
};

// Generate structured data
export const generateStructuredData = (type = 'WebSite', data = {}) => {
  const baseData = {
    '@context': 'https://schema.org',
    '@type': type,
    name: SEO_CONFIG.siteName,
    description: SEO_CONFIG.defaultDescription,
    url: SEO_CONFIG.siteUrl,
    author: {
      '@type': 'Organization',
      name: SEO_CONFIG.author
    }
  };

  return { ...baseData, ...data };
};
