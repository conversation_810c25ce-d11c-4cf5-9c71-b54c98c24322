import { useState, useEffect } from 'react';
import { BREAKPOINTS } from '../constants/breakpoints';


/**
 * Custom hook to detect current screen size and provide responsive utilities
 * @returns {Object} Object containing current screen type and utility functions
 */
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  const [currentBreakpoint, setCurrentBreakpoint] = useState('desktop');

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setScreenSize({ width, height });

      // Determine current breakpoint
      if (width <= BREAKPOINTS.PHONE.MAX) {
        setCurrentBreakpoint('phone');
      } else if (width >= BREAKPOINTS.TABLET.MIN && width <= BREAKPOINTS.TABLET.MAX) {
        setCurrentBreakpoint('tablet');
      } else {
        setCurrentBreakpoint('desktop');
      }
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Utility functions
  const isPhone = currentBreakpoint === 'phone';
  const isTablet = currentBreakpoint === 'tablet';
  const isDesktop = currentBreakpoint === 'desktop';
  const isMobile = isPhone || isTablet;

  // Media query matchers
  const matchesPhone = screenSize.width <= BREAKPOINTS.PHONE.MAX;
  const matchesTablet = screenSize.width >= BREAKPOINTS.TABLET.MIN && screenSize.width <= BREAKPOINTS.TABLET.MAX;
  const matchesDesktop = screenSize.width >= BREAKPOINTS.DESKTOP.MIN;

  // Responsive value selector
  const getResponsiveValue = (values) => {
    if (typeof values === 'object' && values !== null) {
      if (isPhone && values.phone !== undefined) return values.phone;
      if (isTablet && values.tablet !== undefined) return values.tablet;
      if (isDesktop && values.desktop !== undefined) return values.desktop;

      // Fallback logic
      if (isMobile && values.mobile !== undefined) return values.mobile;
      if (values.default !== undefined) return values.default;

      // Return first available value
      return values.phone || values.tablet || values.desktop || values.mobile;
    }
    return values;
  };

  return {
    // Screen dimensions
    screenSize,
    width: screenSize.width,
    height: screenSize.height,

    // Current breakpoint
    currentBreakpoint,

    // Boolean checks
    isPhone,
    isTablet,
    isDesktop,
    isMobile,

    // Media query matchers
    matchesPhone,
    matchesTablet,
    matchesDesktop,

    // Utility functions
    getResponsiveValue,

    // Breakpoint constants for reference
    breakpoints: BREAKPOINTS
  };
};

/**
 * Hook for using media queries directly
 * @param {string} query - CSS media query string
 * @returns {boolean} Whether the media query matches
 */
export const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event) => setMatches(event.matches);
    mediaQuery.addEventListener('change', handler);

    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
};

export default useResponsive;
