import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import styled from 'styled-components';

// Import components and pages
import ResponsiveLayout from './layouts/ResponsiveLayout';
import Navigation from './components/Navigation';
import { Home, About, Service, Blog, Contact } from './pages';

// Import styles
import './styles/globals.css';

// Import constants
import { COLORS } from './constants/breakpoints';

// Styled components
const FooterContent = styled.div`
  text-align: center;
  color: #666;

  p {
    margin: 0;
  }

  a {
    color: ${COLORS.PRIMARY};
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

// Navigation items
const navigationItems = [
  {
    label: 'Home',
    href: '/',
    key: 'home'
  },
  {
    label: 'About',
    href: '/about',
    key: 'about'
  },
  {
    label: 'Work',
    href: '/work',
    key: 'work'
  },
  {
    label: 'Service',
    href: '/service',
    key: 'service'
  },
  {
    label: 'Blog',
    href: '/blog',
    key: 'blog'
  },
  {
    label: "Let's Talk",
    href: '/contact',
    key: 'contact'
  }
];

// App content component to access location
const AppContent = () => {
  const location = useLocation();

  // Update navigation items with active state
  const navItems = navigationItems.map(item => ({
    ...item,
    active: location.pathname === item.href,
    onClick: (e) => {
      // Handle navigation - you can add custom logic here
      // For now, let the default href behavior work
    }
  }));

  // Header component
  const header = (
    <Navigation
      logo="Langit Biru"
      items={navItems}
      logoColor={COLORS.PRIMARY}
    />
  );

  // Footer component
  const footer = (
    <FooterContent>
      <p>&copy; 2024 Langit Biru. Built with React & Vite.</p>
      <p>
        <a href="https://github.com" target="_blank" rel="noopener noreferrer">
          GitHub
        </a>
        {' | '}
        <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer">
          LinkedIn
        </a>
      </p>
    </FooterContent>
  );

  return (
    <ResponsiveLayout
      header={header}
      footer={footer}
      stickyHeader={true}
    >
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/work" element={
          <div style={{ padding: '2rem', textAlign: 'center' }}>
            <h1>Work Page</h1>
            <p>This page is under construction.</p>
          </div>
        } />
        <Route path="/service" element={<Service />} />
        <Route path="/blog" element={<Blog />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="*" element={
          <div style={{ padding: '2rem', textAlign: 'center' }}>
            <h1>404 - Page Not Found</h1>
            <p>The page you're looking for doesn't exist.</p>
          </div>
        } />
      </Routes>
    </ResponsiveLayout>
  );
};

// Main App component
const App = () => {
  return (
    <HelmetProvider>
      <Router>
        <AppContent />
      </Router>
    </HelmetProvider>
  );
};

export default App;
