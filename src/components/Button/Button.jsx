import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { COLORS, SPACING, FONT_SIZES, TRANSITIONS, BORDER_RADIUS } from '../../constants/breakpoints';

// Base button styles
const StyledButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${SPACING.SM};
  padding: ${props => {
    switch (props.size) {
      case 'small': return `${SPACING.SM} ${SPACING.MD}`;
      case 'large': return `${SPACING.LG} ${SPACING.XL}`;
      default: return `${SPACING.MD} ${SPACING.LG}`;
    }
  }};
  font-size: ${props => {
    switch (props.size) {
      case 'small': return FONT_SIZES.SM;
      case 'large': return FONT_SIZES.LG;
      default: return FONT_SIZES.BASE;
    }
  }};
  font-weight: 500;
  border: 2px solid transparent;
  border-radius: ${BORDER_RADIUS.MD};
  cursor: pointer;
  transition: all ${TRANSITIONS.FAST};
  text-decoration: none;
  font-family: inherit;
  line-height: 1;
  min-height: ${props => {
    switch (props.size) {
      case 'small': return '32px';
      case 'large': return '48px';
      default: return '40px';
    }
  }};
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
  }
  
  /* Variant styles */
  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background-color: ${COLORS.PRIMARY};
          color: white;
          border-color: ${COLORS.PRIMARY};
          
          &:hover:not(:disabled) {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
      
      case 'secondary':
        return `
          background-color: ${COLORS.SECONDARY};
          color: white;
          border-color: ${COLORS.SECONDARY};
          
          &:hover:not(:disabled) {
            background-color: #545b62;
            border-color: #545b62;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
      
      case 'outline':
        return `
          background-color: transparent;
          color: ${COLORS.PRIMARY};
          border-color: ${COLORS.PRIMARY};
          
          &:hover:not(:disabled) {
            background-color: ${COLORS.PRIMARY};
            color: white;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
      
      case 'ghost':
        return `
          background-color: transparent;
          color: ${COLORS.PRIMARY};
          border-color: transparent;
          
          &:hover:not(:disabled) {
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
      
      case 'danger':
        return `
          background-color: ${COLORS.DANGER};
          color: white;
          border-color: ${COLORS.DANGER};
          
          &:hover:not(:disabled) {
            background-color: #c82333;
            border-color: #c82333;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
      
      case 'success':
        return `
          background-color: ${COLORS.SUCCESS};
          color: white;
          border-color: ${COLORS.SUCCESS};
          
          &:hover:not(:disabled) {
            background-color: #218838;
            border-color: #218838;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
      
      default:
        return `
          background-color: ${COLORS.LIGHT};
          color: ${COLORS.DARK};
          border-color: ${COLORS.LIGHT};
          
          &:hover:not(:disabled) {
            background-color: #e2e6ea;
            border-color: #e2e6ea;
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
        `;
    }
  }}
  
  /* Full width */
  ${props => props.fullWidth && `
    width: 100%;
  `}
  
  /* Loading state */
  ${props => props.loading && `
    pointer-events: none;
    opacity: 0.7;
  `}
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

/**
 * Button Component
 * A flexible button component with multiple variants and sizes
 */
const Button = ({
  children,
  variant = 'default',
  size = 'medium',
  fullWidth = false,
  loading = false,
  disabled = false,
  onClick,
  type = 'button',
  className,
  ...props
}) => {
  const handleClick = (e) => {
    if (loading || disabled) return;
    if (onClick) onClick(e);
  };

  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      loading={loading}
      disabled={disabled || loading}
      onClick={handleClick}
      type={type}
      className={className}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {children}
    </StyledButton>
  );
};

Button.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['default', 'primary', 'secondary', 'outline', 'ghost', 'danger', 'success']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  fullWidth: PropTypes.bool,
  loading: PropTypes.bool,
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  className: PropTypes.string
};

export default Button;
