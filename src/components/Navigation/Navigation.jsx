import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { BREAKPOINTS, COLORS, SPACING, TRANSITIONS, Z_INDEX, FONT_SIZES } from '../../constants/breakpoints';
import logoImage from '../../assets/images/LOGO LANGIT BIRU.png';

// Styled components
const Nav = styled.nav`
  width: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  position: fixed;
  top: 0;
  left: 0;
  z-index: ${Z_INDEX.HEADER};
  transition: all ${TRANSITIONS.MEDIUM};
`;

const NavContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${SPACING.LG};
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: 0 ${SPACING.MD};
    height: 70px;
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding: 0 ${SPACING.MD};
    height: 60px;
  }
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-weight: 700;
  font-size: ${FONT_SIZES.LG};

  img {
    height: 40px;
    width: auto;
    margin-right: ${SPACING.SM};

    @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
      height: 35px;
    }

    @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
      height: 30px;
    }
  }

  .logo-text {
    @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
      display: none;
    }
  }
`;

// Desktop Navigation
const DesktopNav = styled.div`
  display: flex;
  align-items: center;
  gap: ${SPACING.XL};

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    gap: ${SPACING.LG};
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    display: none;
  }
`;

const NavLink = styled(Link)`
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: ${FONT_SIZES.BASE};
  padding: ${SPACING.SM} 0;
  position: relative;
  transition: all ${TRANSITIONS.FAST};

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: ${COLORS.PRIMARY};
    transform: scaleX(0);
    transition: transform ${TRANSITIONS.FAST};
  }

  &:hover {
    color: ${COLORS.PRIMARY};

    &::after {
      transform: scaleX(1);
    }
  }

  &.active {
    color: ${COLORS.PRIMARY};

    &::after {
      transform: scaleX(1);
    }
  }

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    font-size: ${FONT_SIZES.SM};
  }
`;

const CTAButton = styled(Link)`
  background-color: ${COLORS.PRIMARY};
  color: white;
  padding: ${SPACING.SM} ${SPACING.LG};
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: ${FONT_SIZES.SM};
  transition: all ${TRANSITIONS.FAST};

  &:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
  }

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: ${SPACING.XS} ${SPACING.MD};
    font-size: ${FONT_SIZES.XS};
  }
`;

// Mobile Navigation
const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: ${SPACING.XS};

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    display: block;
  }
`;

const MobileMenu = styled.div`
  display: none;
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  padding: ${SPACING.LG};
  transform: translateY(${props => props.isOpen ? '0' : '-100%'});
  transition: transform ${TRANSITIONS.MEDIUM};
  z-index: ${Z_INDEX.DROPDOWN};

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    display: block;
  }
`;

const MobileNavLink = styled(Link)`
  display: block;
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: ${FONT_SIZES.LG};
  padding: ${SPACING.MD} 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all ${TRANSITIONS.FAST};

  &:hover {
    color: ${COLORS.PRIMARY};
    padding-left: ${SPACING.SM};
  }

  &.active {
    color: ${COLORS.PRIMARY};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const MobileCTAButton = styled(Link)`
  display: block;
  background-color: ${COLORS.PRIMARY};
  color: white;
  padding: ${SPACING.MD};
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  margin-top: ${SPACING.LG};
  transition: all ${TRANSITIONS.FAST};

  &:hover {
    background-color: #0056b3;
  }
`;

/**
 * Navigation Component
 * Responsive navigation with logo and mobile menu
 */
const Navigation = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const isActive = (path) => location.pathname === path;

  const navItems = [
    { path: '/', label: 'Work' },
    { path: '/service', label: 'Service' },
    { path: '/about', label: 'About' },
    { path: '/blog', label: 'Blog' }
  ];

  return (
    <>
      <Nav>
        <NavContainer>
          <Logo to="/" onClick={closeMobileMenu}>
            <img src={logoImage} alt="Langit Biru" />
            <span className="logo-text">Langit Biru</span>
          </Logo>

          <DesktopNav>
            {navItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={isActive(item.path) ? 'active' : ''}
              >
                {item.label}
              </NavLink>
            ))}
            <CTAButton to="/contact">Let's Talk</CTAButton>
          </DesktopNav>

          <MobileMenuButton onClick={toggleMobileMenu}>
            {isMobileMenuOpen ? '✕' : '☰'}
          </MobileMenuButton>
        </NavContainer>
      </Nav>

      <MobileMenu isOpen={isMobileMenuOpen}>
        {navItems.map((item) => (
          <MobileNavLink
            key={item.path}
            to={item.path}
            className={isActive(item.path) ? 'active' : ''}
            onClick={closeMobileMenu}
          >
            {item.label}
          </MobileNavLink>
        ))}
        <MobileCTAButton to="/contact" onClick={closeMobileMenu}>
          Let's Talk
        </MobileCTAButton>
      </MobileMenu>
    </>
  );
};

export default Navigation;
