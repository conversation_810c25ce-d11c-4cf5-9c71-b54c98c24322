import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { BREAKPOINTS, SPACING } from '../../constants/breakpoints';

// Container component
const GridContainer = styled.div`
  width: 100%;
  max-width: ${props => props.maxWidth || '1200px'};
  margin: 0 auto;
  padding: 0 ${props => props.padding || SPACING.MD};
  
  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding: 0 ${props => props.phonePadding || SPACING.SM};
  }
`;

// Row component
const GridRow = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: 0 -${props => props.gutter || SPACING.SM};
  
  ${props => props.noGutters && `
    margin: 0;
  `}
  
  ${props => props.alignItems && `
    align-items: ${props.alignItems};
  `}
  
  ${props => props.justifyContent && `
    justify-content: ${props.justifyContent};
  `}
  
  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    ${props => props.phoneDirection && `
      flex-direction: ${props.phoneDirection};
    `}
  }
`;

// Column component
const GridCol = styled.div`
  flex: ${props => {
    if (props.auto) return '0 0 auto';
    if (props.desktop) return `0 0 ${(props.desktop / 12) * 100}%`;
    return '1';
  }};
  
  padding: 0 ${props => props.noGutters ? '0' : (props.gutter || SPACING.SM)};
  
  /* Desktop styles */
  @media ${BREAKPOINTS.DESKTOP.MEDIA_QUERY} {
    ${props => props.desktop && `
      flex: 0 0 ${(props.desktop / 12) * 100}%;
      max-width: ${(props.desktop / 12) * 100}%;
    `}
    
    ${props => props.desktopOffset && `
      margin-left: ${(props.desktopOffset / 12) * 100}%;
    `}
    
    ${props => props.desktopOrder && `
      order: ${props.desktopOrder};
    `}
  }
  
  /* Tablet styles */
  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    ${props => props.tablet && `
      flex: 0 0 ${(props.tablet / 12) * 100}%;
      max-width: ${(props.tablet / 12) * 100}%;
    `}
    
    ${props => props.tabletOffset && `
      margin-left: ${(props.tabletOffset / 12) * 100}%;
    `}
    
    ${props => props.tabletOrder && `
      order: ${props.tabletOrder};
    `}
  }
  
  /* Phone styles */
  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    ${props => props.phone && `
      flex: 0 0 ${(props.phone / 12) * 100}%;
      max-width: ${(props.phone / 12) * 100}%;
    `}
    
    ${props => props.phoneOffset && `
      margin-left: ${(props.phoneOffset / 12) * 100}%;
    `}
    
    ${props => props.phoneOrder && `
      order: ${props.phoneOrder};
    `}
    
    ${props => !props.phone && !props.tablet && !props.desktop && `
      flex: 0 0 100%;
      max-width: 100%;
    `}
  }
`;

/**
 * Container Component
 * Provides a responsive container with max-width and padding
 */
export const Container = ({ 
  children, 
  maxWidth, 
  padding, 
  phonePadding, 
  fluid = false,
  className 
}) => {
  if (fluid) {
    return (
      <div className={className} style={{ width: '100%', padding: padding || SPACING.MD }}>
        {children}
      </div>
    );
  }

  return (
    <GridContainer 
      maxWidth={maxWidth} 
      padding={padding} 
      phonePadding={phonePadding}
      className={className}
    >
      {children}
    </GridContainer>
  );
};

Container.propTypes = {
  children: PropTypes.node.isRequired,
  maxWidth: PropTypes.string,
  padding: PropTypes.string,
  phonePadding: PropTypes.string,
  fluid: PropTypes.bool,
  className: PropTypes.string
};

/**
 * Row Component
 * Creates a flex container for columns
 */
export const Row = ({ 
  children, 
  gutter, 
  noGutters = false, 
  alignItems, 
  justifyContent,
  phoneDirection,
  className 
}) => {
  return (
    <GridRow 
      gutter={gutter}
      noGutters={noGutters}
      alignItems={alignItems}
      justifyContent={justifyContent}
      phoneDirection={phoneDirection}
      className={className}
    >
      {children}
    </GridRow>
  );
};

Row.propTypes = {
  children: PropTypes.node.isRequired,
  gutter: PropTypes.string,
  noGutters: PropTypes.bool,
  alignItems: PropTypes.oneOf(['flex-start', 'center', 'flex-end', 'stretch', 'baseline']),
  justifyContent: PropTypes.oneOf(['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly']),
  phoneDirection: PropTypes.oneOf(['row', 'column']),
  className: PropTypes.string
};

/**
 * Column Component
 * Responsive column with breakpoint-specific sizing
 */
export const Col = ({ 
  children, 
  desktop, 
  tablet, 
  phone,
  desktopOffset,
  tabletOffset,
  phoneOffset,
  desktopOrder,
  tabletOrder,
  phoneOrder,
  auto = false,
  gutter,
  noGutters = false,
  className 
}) => {
  return (
    <GridCol 
      desktop={desktop}
      tablet={tablet}
      phone={phone}
      desktopOffset={desktopOffset}
      tabletOffset={tabletOffset}
      phoneOffset={phoneOffset}
      desktopOrder={desktopOrder}
      tabletOrder={tabletOrder}
      phoneOrder={phoneOrder}
      auto={auto}
      gutter={gutter}
      noGutters={noGutters}
      className={className}
    >
      {children}
    </GridCol>
  );
};

Col.propTypes = {
  children: PropTypes.node.isRequired,
  desktop: PropTypes.number,
  tablet: PropTypes.number,
  phone: PropTypes.number,
  desktopOffset: PropTypes.number,
  tabletOffset: PropTypes.number,
  phoneOffset: PropTypes.number,
  desktopOrder: PropTypes.number,
  tabletOrder: PropTypes.number,
  phoneOrder: PropTypes.number,
  auto: PropTypes.bool,
  gutter: PropTypes.string,
  noGutters: PropTypes.bool,
  className: PropTypes.string
};

// Export default as an object containing all components
const Grid = {
  Container,
  Row,
  Col
};

export default Grid;
