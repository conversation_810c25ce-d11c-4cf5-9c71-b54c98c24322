import { Helmet } from 'react-helmet-async';
import PropTypes from 'prop-types';
import { SEO_CONFIG, generateStructuredData } from '../../config/seo';

const SEO = ({
  title = SEO_CONFIG.defaultTitle,
  description = SEO_CONFIG.defaultDescription,
  keywords = SEO_CONFIG.defaultKeywords.join(', '),
  image = SEO_CONFIG.defaultImage,
  url = typeof window !== 'undefined' ? window.location.href : SEO_CONFIG.siteUrl,
  type = 'website',
  author = SEO_CONFIG.author,
  siteName = SEO_CONFIG.siteName
}) => {
  const fullTitle = title === SEO_CONFIG.defaultTitle ? title : `${title} | ${SEO_CONFIG.siteName}`;
  const structuredData = generateStructuredData('WebSite', {
    name: siteName,
    description,
    url
  });

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content={siteName} />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={url} />
      <meta property="twitter:title" content={fullTitle} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={image} />

      {/* Additional SEO */}
      <meta name="theme-color" content="#007bff" />
      <link rel="canonical" href={url} />

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

SEO.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  keywords: PropTypes.string,
  image: PropTypes.string,
  url: PropTypes.string,
  type: PropTypes.string,
  author: PropTypes.string,
  siteName: PropTypes.string
};

export default SEO;
