import styled from 'styled-components';
import { Container, Row, Col } from '../components/Grid';
import SEO from '../components/SEO';
import { COLORS, SPACING, FONT_SIZES } from '../constants/breakpoints';

const PageHeader = styled.section`
  background: linear-gradient(135deg, ${COLORS.PRIMARY} 0%, #0056b3 100%);
  color: white;
  padding: ${SPACING.XXL} 0;
  text-align: center;
`;

const PageTitle = styled.h1`
  font-size: ${FONT_SIZES.XXL};
  font-weight: 700;
  margin-bottom: ${SPACING.MD};
`;

const PageSubtitle = styled.p`
  font-size: ${FONT_SIZES.LG};
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const Section = styled.section`
  padding: ${SPACING.XXL} 0;
`;

const SectionTitle = styled.h2`
  font-size: ${FONT_SIZES.XL};
  font-weight: 700;
  text-align: center;
  margin-bottom: ${SPACING.XL};
  color: ${COLORS.DARK};
`;

const ServiceCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: ${SPACING.XL};
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
  
  h3 {
    font-size: ${FONT_SIZES.LG};
    font-weight: 600;
    margin-bottom: ${SPACING.MD};
    color: ${COLORS.PRIMARY};
  }
  
  p {
    color: ${COLORS.SECONDARY};
    line-height: 1.6;
  }
`;

const Service = () => {
  const services = [
    {
      title: 'Web Development',
      description: 'Modern, responsive websites built with the latest technologies and best practices.'
    },
    {
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile applications for iOS and Android.'
    },
    {
      title: 'UI/UX Design',
      description: 'User-centered design solutions that create engaging digital experiences.'
    },
    {
      title: 'Brand Identity',
      description: 'Complete branding solutions from logo design to brand guidelines.'
    },
    {
      title: 'Digital Marketing',
      description: 'Strategic digital marketing campaigns to grow your online presence.'
    },
    {
      title: 'Consulting',
      description: 'Expert consultation on technology strategy and digital transformation.'
    }
  ];

  return (
    <>
      <SEO
        title="Service"
        description="Professional digital services including web development, mobile apps, UI/UX design, and digital marketing solutions."
        keywords="web development, mobile apps, ui/ux design, digital marketing, consulting"
        type="website"
      />
      
      <PageHeader>
        <Container>
          <Row>
            <Col desktop={12} tablet={12} phone={12}>
              <PageTitle>Our Services</PageTitle>
              <PageSubtitle>
                We provide comprehensive digital solutions to help your business grow and succeed in the digital world.
              </PageSubtitle>
            </Col>
          </Row>
        </Container>
      </PageHeader>

      <Section>
        <Container>
          <SectionTitle>What We Offer</SectionTitle>
          <Row>
            {services.map((service, index) => (
              <Col key={index} desktop={4} tablet={6} phone={12}>
                <ServiceCard>
                  <h3>{service.title}</h3>
                  <p>{service.description}</p>
                </ServiceCard>
              </Col>
            ))}
          </Row>
        </Container>
      </Section>
    </>
  );
};

export default Service;
