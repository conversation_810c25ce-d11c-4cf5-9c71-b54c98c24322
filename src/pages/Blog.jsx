import styled from 'styled-components';
import { Container, Row, Col } from '../components/Grid';
import SEO from '../components/SEO';
import { COLORS, SPACING, FONT_SIZES } from '../constants/breakpoints';

const PageHeader = styled.section`
  background: linear-gradient(135deg, ${COLORS.SECONDARY} 0%, #495057 100%);
  color: white;
  padding: ${SPACING.XXL} 0;
  text-align: center;
`;

const PageTitle = styled.h1`
  font-size: ${FONT_SIZES.XXL};
  font-weight: 700;
  margin-bottom: ${SPACING.MD};
`;

const PageSubtitle = styled.p`
  font-size: ${FONT_SIZES.LG};
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const Section = styled.section`
  padding: ${SPACING.XXL} 0;
`;

const SectionTitle = styled.h2`
  font-size: ${FONT_SIZES.XL};
  font-weight: 700;
  text-align: center;
  margin-bottom: ${SPACING.XL};
  color: ${COLORS.DARK};
`;

const BlogCard = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  margin-bottom: ${SPACING.XL};
  
  &:hover {
    transform: translateY(-5px);
  }
`;

const BlogImage = styled.div`
  height: 200px;
  background: linear-gradient(135deg, ${COLORS.PRIMARY} 0%, #0056b3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: ${FONT_SIZES.LG};
  font-weight: 600;
`;

const BlogContent = styled.div`
  padding: ${SPACING.XL};
  
  h3 {
    font-size: ${FONT_SIZES.LG};
    font-weight: 600;
    margin-bottom: ${SPACING.MD};
    color: ${COLORS.DARK};
  }
  
  p {
    color: ${COLORS.SECONDARY};
    line-height: 1.6;
    margin-bottom: ${SPACING.MD};
  }
  
  .meta {
    font-size: ${FONT_SIZES.SM};
    color: ${COLORS.LIGHT};
    margin-bottom: ${SPACING.SM};
  }
`;

const Blog = () => {
  const blogPosts = [
    {
      title: 'Modern Web Development Trends 2024',
      excerpt: 'Explore the latest trends in web development including AI integration, serverless architecture, and progressive web apps.',
      date: 'December 15, 2024',
      category: 'Web Development'
    },
    {
      title: 'The Future of Mobile App Development',
      excerpt: 'How cross-platform frameworks and AI are shaping the future of mobile application development.',
      date: 'December 10, 2024',
      category: 'Mobile Development'
    },
    {
      title: 'UI/UX Design Best Practices',
      excerpt: 'Essential principles and practices for creating user-centered designs that convert and engage.',
      date: 'December 5, 2024',
      category: 'Design'
    },
    {
      title: 'Building Scalable React Applications',
      excerpt: 'Learn how to structure and optimize React applications for better performance and maintainability.',
      date: 'November 28, 2024',
      category: 'React'
    },
    {
      title: 'The Importance of Responsive Design',
      excerpt: 'Why responsive design is crucial for modern websites and how to implement it effectively.',
      date: 'November 20, 2024',
      category: 'Web Design'
    },
    {
      title: 'Digital Marketing in 2024',
      excerpt: 'Latest strategies and trends in digital marketing that businesses should adopt this year.',
      date: 'November 15, 2024',
      category: 'Marketing'
    }
  ];

  return (
    <>
      <SEO
        title="Blog"
        description="Latest insights, tutorials, and trends in web development, mobile apps, design, and digital marketing."
        keywords="blog, web development, mobile apps, design, digital marketing, tutorials"
        type="website"
      />
      
      <PageHeader>
        <Container>
          <Row>
            <Col desktop={12} tablet={12} phone={12}>
              <PageTitle>Our Blog</PageTitle>
              <PageSubtitle>
                Insights, tutorials, and the latest trends in technology and digital innovation.
              </PageSubtitle>
            </Col>
          </Row>
        </Container>
      </PageHeader>

      <Section>
        <Container>
          <SectionTitle>Latest Articles</SectionTitle>
          <Row>
            {blogPosts.map((post, index) => (
              <Col key={index} desktop={6} tablet={6} phone={12}>
                <BlogCard>
                  <BlogImage>
                    {post.category}
                  </BlogImage>
                  <BlogContent>
                    <div className="meta">{post.date} • {post.category}</div>
                    <h3>{post.title}</h3>
                    <p>{post.excerpt}</p>
                  </BlogContent>
                </BlogCard>
              </Col>
            ))}
          </Row>
        </Container>
      </Section>
    </>
  );
};

export default Blog;
