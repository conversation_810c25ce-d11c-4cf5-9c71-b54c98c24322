import styled from 'styled-components';
import { Container, Row, Col } from '../components/Grid';
import Button from '../components/Button';
import SEO from '../components/SEO';
import { COLORS, SPACING, FONT_SIZES } from '../constants/breakpoints';

const PageHeader = styled.section`
  background: linear-gradient(135deg, ${COLORS.SUCCESS} 0%, #218838 100%);
  color: white;
  padding: ${SPACING.XXL} 0;
  text-align: center;
`;

const PageTitle = styled.h1`
  font-size: ${FONT_SIZES.XXL};
  font-weight: 700;
  margin-bottom: ${SPACING.MD};
`;

const PageSubtitle = styled.p`
  font-size: ${FONT_SIZES.LG};
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const Section = styled.section`
  padding: ${SPACING.XXL} 0;
`;

const ContactForm = styled.form`
  background: white;
  border-radius: 12px;
  padding: ${SPACING.XXL};
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: ${SPACING.LG};
`;

const Label = styled.label`
  display: block;
  font-weight: 600;
  margin-bottom: ${SPACING.SM};
  color: ${COLORS.DARK};
`;

const Input = styled.input`
  width: 100%;
  padding: ${SPACING.MD};
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: ${FONT_SIZES.BASE};
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: ${COLORS.PRIMARY};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${SPACING.MD};
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: ${FONT_SIZES.BASE};
  min-height: 120px;
  resize: vertical;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: ${COLORS.PRIMARY};
  }
`;

const ContactInfo = styled.div`
  background: ${COLORS.LIGHT};
  border-radius: 12px;
  padding: ${SPACING.XXL};
  
  h3 {
    font-size: ${FONT_SIZES.LG};
    font-weight: 600;
    margin-bottom: ${SPACING.LG};
    color: ${COLORS.DARK};
  }
`;

const ContactItem = styled.div`
  margin-bottom: ${SPACING.LG};
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .label {
    font-weight: 600;
    color: ${COLORS.PRIMARY};
    margin-bottom: ${SPACING.XS};
  }
  
  .value {
    color: ${COLORS.SECONDARY};
  }
`;

const Contact = () => {
  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    alert('Thank you for your message! We will get back to you soon.');
  };

  return (
    <>
      <SEO
        title="Contact"
        description="Get in touch with Langit Biru. Let's discuss your project and how we can help bring your ideas to life."
        keywords="contact, get in touch, project inquiry, consultation"
        type="website"
      />
      
      <PageHeader>
        <Container>
          <Row>
            <Col desktop={12} tablet={12} phone={12}>
              <PageTitle>Let's Talk</PageTitle>
              <PageSubtitle>
                Ready to start your project? Get in touch and let's discuss how we can help bring your ideas to life.
              </PageSubtitle>
            </Col>
          </Row>
        </Container>
      </PageHeader>

      <Section>
        <Container>
          <Row>
            <Col desktop={8} tablet={12} phone={12}>
              <ContactForm onSubmit={handleSubmit}>
                <h2 style={{ marginBottom: SPACING.XL, color: COLORS.DARK }}>
                  Send us a message
                </h2>
                
                <Row>
                  <Col desktop={6} tablet={6} phone={12}>
                    <FormGroup>
                      <Label htmlFor="name">Name *</Label>
                      <Input
                        type="text"
                        id="name"
                        name="name"
                        required
                        placeholder="Your full name"
                      />
                    </FormGroup>
                  </Col>
                  <Col desktop={6} tablet={6} phone={12}>
                    <FormGroup>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        type="email"
                        id="email"
                        name="email"
                        required
                        placeholder="<EMAIL>"
                      />
                    </FormGroup>
                  </Col>
                </Row>
                
                <FormGroup>
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    type="text"
                    id="subject"
                    name="subject"
                    required
                    placeholder="What's this about?"
                  />
                </FormGroup>
                
                <FormGroup>
                  <Label htmlFor="message">Message *</Label>
                  <TextArea
                    id="message"
                    name="message"
                    required
                    placeholder="Tell us about your project..."
                  />
                </FormGroup>
                
                <Button type="submit" variant="primary" size="large" fullWidth>
                  Send Message
                </Button>
              </ContactForm>
            </Col>
            
            <Col desktop={4} tablet={12} phone={12}>
              <ContactInfo>
                <h3>Contact Information</h3>
                
                <ContactItem>
                  <div className="label">Email</div>
                  <div className="value"><EMAIL></div>
                </ContactItem>
                
                <ContactItem>
                  <div className="label">Phone</div>
                  <div className="value">+62 ************</div>
                </ContactItem>
                
                <ContactItem>
                  <div className="label">Address</div>
                  <div className="value">
                    Jakarta, Indonesia<br />
                    Based in Jakarta - Indonesia
                  </div>
                </ContactItem>
                
                <ContactItem>
                  <div className="label">Business Hours</div>
                  <div className="value">
                    Monday - Friday<br />
                    9:00 AM - 6:00 PM (WIB)
                  </div>
                </ContactItem>
              </ContactInfo>
            </Col>
          </Row>
        </Container>
      </Section>
    </>
  );
};

export default Contact;
