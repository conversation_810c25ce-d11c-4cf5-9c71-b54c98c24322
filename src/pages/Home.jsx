import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { Container, Row, Col } from '../components/Grid';
import Button from '../components/Button';
import SEO from '../components/SEO';
import { BREAKPOINTS, COLORS, SPACING, FONT_SIZES, TRANSITIONS } from '../constants/breakpoints';

// Hero Section with Image Carousel
const HeroSection = styled.section`
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    height: 80vh;
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    height: 70vh;
  }
`;

const HeroBackground = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, ${COLORS.PRIMARY} 0%, #0056b3 100%);
  z-index: 1;
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 ${SPACING.LG};
`;

const HeroTitle = styled.h1`
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  line-height: 0.9;
  margin-bottom: ${SPACING.MD};
  letter-spacing: -0.02em;

  .title-line {
    display: block;
  }
`;

const HeroSubtitle = styled.p`
  font-size: ${FONT_SIZES.LG};
  margin-bottom: ${SPACING.XL};
  opacity: 0.9;
  font-weight: 300;
`;

const HeroLocation = styled.p`
  font-size: ${FONT_SIZES.SM};
  opacity: 0.8;
  margin-bottom: ${SPACING.XXL};
  font-weight: 400;
`;

// Section Styling
const Section = styled.section`
  padding: ${SPACING.XXXL} 0;

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: ${SPACING.XXL} 0;
  }

  @media ${BREAKPOINTS.PHONE.MEDIA_QUERY} {
    padding: ${SPACING.XL} 0;
  }
`;

const SectionLabel = styled.span`
  font-size: ${FONT_SIZES.SM};
  color: ${COLORS.PRIMARY};
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: ${SPACING.MD};
  display: block;
`;

const SectionNumber = styled.span`
  font-size: ${FONT_SIZES.LG};
  color: ${COLORS.PRIMARY};
  font-weight: 700;
  margin-right: ${SPACING.SM};
`;

const SectionTitle = styled.h2`
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: ${SPACING.XL};
  color: ${COLORS.DARK};
`;

const SectionDescription = styled.p`
  font-size: ${FONT_SIZES.LG};
  color: #666;
  margin-bottom: ${SPACING.XXL};
  max-width: 600px;
`;

// About Section Styling
const AboutSection = styled(Section)`
  background: #f8f9fa;
`;

const AboutContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${SPACING.XXXL};
  align-items: center;

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    grid-template-columns: 1fr;
    gap: ${SPACING.XXL};
  }
`;

const AboutText = styled.div`
  h3 {
    font-size: clamp(1.5rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: ${SPACING.LG};
    color: ${COLORS.DARK};

    .highlight {
      color: ${COLORS.PRIMARY};
    }
  }

  p {
    font-size: ${FONT_SIZES.BASE};
    line-height: 1.6;
    color: #666;
    margin-bottom: ${SPACING.LG};
  }
`;

// Projects Section
const ProjectCard = styled(Link)`
  display: block;
  text-decoration: none;
  color: inherit;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all ${TRANSITIONS.MEDIUM};

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`;

const ProjectImage = styled.div`
  height: 250px;
  background: ${props => props.bg || COLORS.PRIMARY};
  background-size: cover;
  background-position: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 123, 255, 0.8), rgba(0, 86, 179, 0.8));
    opacity: 0;
    transition: opacity ${TRANSITIONS.MEDIUM};
  }

  ${ProjectCard}:hover &::after {
    opacity: 1;
  }
`;

const ProjectContent = styled.div`
  padding: ${SPACING.XL};

  h3 {
    font-size: ${FONT_SIZES.XL};
    font-weight: 700;
    margin-bottom: ${SPACING.SM};
    color: ${COLORS.DARK};
  }

  .year {
    font-size: ${FONT_SIZES.SM};
    color: ${COLORS.PRIMARY};
    font-weight: 600;
  }
`;

// Services Section
const ServiceCard = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all ${TRANSITIONS.MEDIUM};

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }
`;

const ServiceImage = styled.div`
  height: 200px;
  background: ${props => props.bg || COLORS.PRIMARY};
  background-size: cover;
  background-position: center;
`;

const ServiceContent = styled.div`
  padding: ${SPACING.XL};

  h3 {
    font-size: ${FONT_SIZES.XL};
    font-weight: 700;
    margin-bottom: ${SPACING.SM};
    color: ${COLORS.DARK};
  }

  .service-type {
    font-size: ${FONT_SIZES.SM};
    color: ${COLORS.PRIMARY};
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: ${SPACING.MD};
  }

  p {
    color: #666;
    line-height: 1.6;
    margin-bottom: ${SPACING.LG};
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      color: #666;
      margin-bottom: ${SPACING.XS};
      position: relative;
      padding-left: ${SPACING.MD};

      &::before {
        content: '•';
        color: ${COLORS.PRIMARY};
        position: absolute;
        left: 0;
      }
    }
  }
`;

const Home = () => {
  const projects = [
    {
      id: 1,
      title: 'BANK INDONESIA',
      year: '2020',
      image: 'https://images.unsplash.com/photo-*************-178a50c2df87?w=600&h=400&fit=crop',
      link: '/work/bank-indonesia'
    },
    {
      id: 2,
      title: 'APMF',
      year: '2024',
      image: 'https://images.unsplash.com/photo-*************-379afb476865?w=600&h=400&fit=crop',
      link: '/work/apmf'
    },
    {
      id: 3,
      title: 'SAMPOERNA ACADEMY',
      year: '2024',
      image: 'https://images.unsplash.com/photo-*************-8d25f7d46678?w=600&h=400&fit=crop',
      link: '/work/sampoerna-academy'
    },
    {
      id: 4,
      title: 'SPOTV',
      year: '2024',
      image: 'https://images.unsplash.com/photo-*************-81342ee5ff30?w=600&h=400&fit=crop',
      link: '/work/spotv'
    }
  ];

  const services = [
    {
      id: 1,
      title: 'Event Strategy',
      type: 'Branding Services',
      description: 'Strong & cohesive brand identity to connect with your audience.',
      features: ['Brand Communication', 'Digital Presence', 'Visual Design', 'Event Mechanism'],
      image: 'https://images.unsplash.com/photo-**********-b413da4baf72?w=400&h=300&fit=crop'
    },
    {
      id: 2,
      title: 'Booth Design & Production',
      type: 'Booth Services',
      description: 'Custom booth production that engage users and drive traffic.',
      features: ['3D Design', 'Budgeting', 'Detail Material', 'Maintenance'],
      image: 'https://images.unsplash.com/photo-*************-178a50c2df87?w=400&h=300&fit=crop'
    },
    {
      id: 3,
      title: 'Brand Merch',
      type: 'Merchandise Service',
      description: 'Awareness through high quality and brand-centered merchandise solutions.',
      features: ['Mock Up Design', 'Distribution', 'Package'],
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop'
    }
  ];

  return (
    <>
      <SEO
        title="Home"
        description="Langit Biru - Crafting impactful brand moment and visibilities that drive growth and success. Based in Jakarta, Indonesia."
        keywords="event management, brand activation, booth design, merchandise, jakarta, indonesia"
        type="website"
      />

      {/* Hero Section */}
      <HeroSection>
        <HeroBackground />
        <HeroContent>
          <HeroTitle>
            <span className="title-line">LANGIT</span>
            <span className="title-line">BIRU</span>
          </HeroTitle>
          <HeroLocation>(Based in Jakarta - Indonesia)</HeroLocation>
          <HeroSubtitle>
            Crafting impactful brand moment and visibilities that drive growth and success.
          </HeroSubtitle>
        </HeroContent>
      </HeroSection>

      {/* About Section */}
      <AboutSection>
        <Container>
          <AboutContent>
            <div>
              <SectionLabel>(About Us)</SectionLabel>
              <AboutText>
                <h3>
                  Creative <span className="highlight">EVENT,</span><br/>
                  powerful <span className="highlight">MOMENTS.</span>
                </h3>
                <p>
                  We believe great events do more than just happen—they leave an impact.
                  We're experts at creating SAFE, SEAMLESS, and UNFORGETTABLE brand activations
                  that connect people and spark meaningful experiences.
                </p>
                <p>
                  From concept to execution, we prioritize COMFORT and PRECISION, ensuring every
                  detail resonates with your vision. Whether it's a grand product launch or an
                  intimate team-building experience, we're here to bring your brand story to life
                  with creativity, care, and purpose.
                </p>
                <p>Let's make moments that matter, together.</p>
                <Button as={Link} to="/about" variant="primary">
                  More About Us
                </Button>
              </AboutText>
            </div>
            <div>
              {/* Placeholder for image or additional content */}
            </div>
          </AboutContent>
        </Container>
      </AboutSection>

      {/* Projects Section */}
      <Section>
        <Container>
          <SectionLabel>(Selected Work)</SectionLabel>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: SPACING.XXL }}>
            <SectionNumber>(01)</SectionNumber>
            <div>
              <SectionTitle>Projects</SectionTitle>
              <SectionDescription>
                Explore our recent projects showcasing creativity, innovation, and impactful activation solutions.
              </SectionDescription>
            </div>
          </div>
          <Row>
            {projects.map((project) => (
              <Col key={project.id} desktop={6} tablet={6} phone={12}>
                <ProjectCard to={project.link}>
                  <ProjectImage bg={`url(${project.image})`} />
                  <ProjectContent>
                    <h3>{project.title}</h3>
                    <span className="year">({project.year})</span>
                  </ProjectContent>
                </ProjectCard>
              </Col>
            ))}
          </Row>
        </Container>
      </Section>

      {/* Services Section */}
      <Section style={{ backgroundColor: '#f8f9fa' }}>
        <Container>
          <SectionLabel>(What we do)</SectionLabel>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: SPACING.XXL }}>
            <SectionNumber>(02)</SectionNumber>
            <div>
              <SectionTitle>Services</SectionTitle>
              <SectionDescription>
                Discover our tailored services designed to elevate your brand, enhance user experience.
              </SectionDescription>
            </div>
          </div>
          <Row>
            {services.map((service) => (
              <Col key={service.id} desktop={4} tablet={6} phone={12}>
                <ServiceCard>
                  <ServiceImage bg={`url(${service.image})`} />
                  <ServiceContent>
                    <div className="service-type">({service.type})</div>
                    <h3>{service.title}</h3>
                    <p>{service.description}</p>
                    <ul>
                      {service.features.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </ServiceContent>
                </ServiceCard>
              </Col>
            ))}
          </Row>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section>
        <Container>
          <Row justifyContent="center">
            <Col desktop={8} tablet={10} phone={12}>
              <div style={{ textAlign: 'center' }}>
                <SectionTitle>Ready to Create Something Amazing?</SectionTitle>
                <SectionDescription style={{ textAlign: 'center', margin: '0 auto', marginBottom: SPACING.XXL }}>
                  Let's discuss your next project and bring your vision to life with our expertise in event management and brand activation.
                </SectionDescription>
                <Button as={Link} to="/contact" variant="primary" size="large">
                  Let's Talk
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      </Section>
    </>
  );
};

export default Home;
