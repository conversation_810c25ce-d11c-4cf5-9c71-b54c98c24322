# Langit Biru - Professional React Application

A professional React application template with responsive design, built with modern tools and best practices.

## 🚀 Features

- **Responsive Design**: Custom breakpoints for Phone (≤809px), Tablet (810-1199px), and Desktop (≥1200px)
- **Modern React**: Built with React 18 and Vite for fast development
- **Professional Structure**: Well-organized folder structure following React best practices
- **Styled Components**: CSS-in-JS with styled-components for component styling
- **Custom Hooks**: useResponsive hook for detecting screen sizes and responsive utilities
- **Grid System**: Flexible responsive grid system with Container, Row, and Col components
- **Navigation**: Responsive navigation with mobile hamburger menu
- **Design System**: Consistent design tokens for colors, spacing, typography, and more
- **TypeScript Ready**: Easy to migrate to TypeScript
- **Performance Optimized**: Built with Vite for fast development and optimized production builds

## 📱 Responsive Breakpoints

- **Phone**: ≤809px (Mobile-first approach)
- **Tablet**: 810px - 1199px
- **Desktop**: ≥1200px (Max container width: 1200px)

## 🛠️ Technologies Used

- React 18
- Vite
- Styled Components
- React Router DOM
- Custom Hooks
- PropTypes
- ESLint & Prettier

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Grid/           # Grid system (Container, Row, Col)
│   ├── Navigation/     # Navigation component
│   └── index.js        # Component exports
├── pages/              # Page components
│   ├── Home.jsx        # Home page
│   ├── About.jsx       # About page
│   └── index.js        # Page exports
├── layouts/            # Layout components
│   └── ResponsiveLayout.jsx
├── hooks/              # Custom React hooks
│   └── useResponsive.js
├── utils/              # Utility functions
│   └── helpers.js
├── services/           # API services
├── contexts/           # React contexts
├── styles/             # Global styles
│   └── globals.css
├── assets/             # Static assets
│   ├── images/
│   ├── icons/
│   └── fonts/
├── constants/          # App constants
│   └── breakpoints.js
├── App.jsx             # Main App component
└── main.jsx            # App entry point
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd langit-biru
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and visit `http://localhost:5173`

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🎨 Design System

### Colors
- Primary: #007bff
- Secondary: #6c757d
- Success: #28a745
- Danger: #dc3545
- Warning: #ffc107
- Info: #17a2b8

### Spacing Scale
- XS: 0.25rem
- SM: 0.5rem
- MD: 1rem
- LG: 1.5rem
- XL: 2rem
- XXL: 3rem
- XXXL: 4rem

### Typography Scale
- XS: 0.75rem
- SM: 0.875rem
- BASE: 1rem
- LG: 1.125rem
- XL: 1.25rem
- XXL: 1.5rem
- XXXL: 1.875rem
- XXXXL: 2.25rem

## 🧩 Components

### Grid System
```jsx
import { Container, Row, Col } from './components/Grid';

<Container>
  <Row>
    <Col desktop={4} tablet={6} phone={12}>
      Content for desktop 4/12, tablet 6/12, phone 12/12
    </Col>
    <Col desktop={8} tablet={6} phone={12}>
      Content for desktop 8/12, tablet 6/12, phone 12/12
    </Col>
  </Row>
</Container>
```

### Responsive Hook
```jsx
import { useResponsive } from './hooks/useResponsive';

const MyComponent = () => {
  const { isPhone, isTablet, isDesktop, currentBreakpoint, width } = useResponsive();

  return (
    <div>
      <p>Current device: {currentBreakpoint}</p>
      <p>Screen width: {width}px</p>
      {isPhone && <p>Mobile view</p>}
      {isTablet && <p>Tablet view</p>}
      {isDesktop && <p>Desktop view</p>}
    </div>
  );
};
```

### Navigation Component
```jsx
import Navigation from './components/Navigation';

const navItems = [
  { label: 'Home', href: '/', active: true },
  { label: 'About', href: '/about' },
  { label: 'Contact', href: '/contact' }
];

<Navigation
  logo="Your Logo"
  items={navItems}
  logoColor="#007bff"
/>
```

## 🎯 Usage Examples

### Responsive Values
```jsx
import { useResponsive } from './hooks/useResponsive';

const MyComponent = () => {
  const { getResponsiveValue } = useResponsive();

  const fontSize = getResponsiveValue({
    phone: '14px',
    tablet: '16px',
    desktop: '18px'
  });

  return <div style={{ fontSize }}>Responsive text</div>;
};
```

### Styled Components with Breakpoints
```jsx
import styled from 'styled-components';
import { BREAKPOINTS } from './constants/breakpoints';

const ResponsiveDiv = styled.div`
  padding: 1rem;

  @media ${BREAKPOINTS.TABLET.MEDIA_QUERY} {
    padding: 1.5rem;
  }

  @media ${BREAKPOINTS.DESKTOP.MEDIA_QUERY} {
    padding: 2rem;
  }
`;
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- React team for the amazing framework
- Vite team for the fast build tool
- Styled Components for CSS-in-JS solution
- All contributors and the open source community
